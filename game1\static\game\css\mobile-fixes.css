/**
 * Mobile Fixes for Corporate Prompt Master Game
 * Comprehensive mobile responsiveness fixes
 */

/* Prevent horizontal scrolling on mobile and remove unnecessary scrollbars */
html, body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Remove any body padding on mobile */
@media (max-width: 992px) {
    body {
        padding-top: 0 !important;
        margin-top: 0 !important;
    }
}

/* MOBILE LAYOUT OVERRIDES - HIGHEST PRIORITY */
@media (max-width: 992px) {
    /* RESET BODY CLASSES ON MOBILE */
    body {
        /* Remove any default sidebar classes */
        overflow-x: hidden !important;
    }

    body:not(.sidebar-visible):not(.right-sidebar-visible) {
        /* Ensure body is normal when no sidebars are visible */
        overflow: auto !important;
        position: static !important;
        width: 100% !important;
        height: 100vh !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Ensure html element doesn't create white space */
    html {
        height: 100vh !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow-x: hidden !important;
    }

    /* ===== LEFT SIDEBAR AS SINGLE ACCORDION PANEL ===== */
    body .sidebar,
    body #sidebar,
    body .left-sidebar,
    .game-container .sidebar {
        position: relative !important;
        top: auto !important;
        left: auto !important;
        width: 100% !important;
        max-width: none !important;
        height: 0 !important; /* Start completely collapsed */
        max-height: 0 !important;
        z-index: 1001 !important;
        background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%) !important;
        overflow: hidden !important; /* Hide all content when collapsed */
        padding: 0 !important; /* No padding when collapsed */
        margin: 0 !important;
        border: none !important;
        border-bottom: 1px solid var(--border-primary) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        transform: none !important;
        order: 0 !important; /* Place left sidebar at the top */
        opacity: 0 !important; /* Start invisible */
    }

    /* Show entire left sidebar when body has sidebar-visible class */
    body.sidebar-visible .sidebar,
    body.sidebar-visible .left-sidebar,
    body.sidebar-visible #sidebar {
        height: auto !important;
        max-height: 500px !important; /* Allow enough space for all content */
        padding: 20px !important; /* Add padding when expanded */
        overflow-y: auto !important; /* Allow scrolling if needed */
        opacity: 1 !important; /* Make visible */
    }

    /* Remove close button for accordion behavior - no longer needed */

    /* ===== RIGHT SIDEBAR AS SINGLE ACCORDION PANEL ===== */
    body .right-sidebar,
    body #right-sidebar,
    .game-container .right-sidebar {
        position: relative !important;
        top: auto !important;
        right: auto !important;
        width: 100% !important;
        max-width: none !important;
        height: 0 !important; /* Start completely collapsed */
        max-height: 0 !important;
        z-index: 999 !important;
        background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%) !important;
        overflow: hidden !important; /* Hide all content when collapsed */
        padding: 0 !important; /* No padding when collapsed */
        margin: 0 !important;
        border: none !important;
        border-bottom: 1px solid var(--border-primary) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        transform: none !important;
        order: 2 !important; /* Place right sidebar after left sidebar */
        opacity: 0 !important; /* Start invisible */
    }

    /* Show entire right sidebar when body has right-sidebar-visible class */
    body.right-sidebar-visible .right-sidebar,
    body.right-sidebar-visible #right-sidebar {
        height: auto !important;
        max-height: 400px !important; /* Allow enough space for all content */
        padding: 20px !important; /* Add padding when expanded */
        overflow-y: auto !important; /* Allow scrolling if needed */
        opacity: 1 !important; /* Make visible */
    }

    /* Remove close button for accordion behavior - no longer needed */

    /* Add padding to sidebar content */
    .sidebar > *,
    .right-sidebar > * {
        padding-left: 20px !important;
        padding-right: 20px !important;
    }

    .sidebar-header,
    .right-sidebar-header {
        padding: 20px !important;
        margin-bottom: 0 !important;
    }

    /* Ensure main content is properly positioned */
    .main-content {
        flex: 1 !important;
        order: 3 !important; /* Place main content after both sidebars */
        min-height: calc(100vh - 60px) !important; /* Account for fixed header */
        overflow-y: auto !important;
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
    }

    /* Hide the main content header since we have a fixed header */
    .main-content .header {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        z-index: 1050 !important;
    }

    /* Ensure app container uses flexbox for proper accordion layout */
    .app-container {
        display: flex !important;
        flex-direction: column !important;
        min-height: 100vh !important;
        height: auto !important; /* Allow height to grow with accordion expansion */
        overflow-x: hidden !important;
        overflow-y: auto !important; /* Allow vertical scrolling when sidebars expand */
        padding-top: 60px !important; /* Account for fixed header */
    }

    /* Ensure proper ordering of elements */
    .sidebar {
        order: 0 !important; /* Left sidebar first */
    }

    .right-sidebar {
        order: 1 !important; /* Right sidebar second */
    }

    .main-content {
        order: 2 !important; /* Main content last */
    }

    /* MOBILE HEADER LAYOUT - ENSURE TOGGLE BUTTONS ARE VISIBLE */
    .header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 12px 16px !important;
        background: var(--bg-secondary) !important;
        border-bottom: 1px solid var(--border-primary) !important;
        position: fixed !important; /* Fixed at top of screen */
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        z-index: 1050 !important;
        order: 1 !important; /* Ensure header is at the top */
        min-height: 60px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .header-left {
        flex: 0 0 auto !important;
    }

    .header-center {
        flex: 1 1 auto !important;
        text-align: center !important;
    }

    .header-right {
        flex: 0 0 auto !important;
        margin-left: auto !important;
    }

    /* Ensure toggle buttons are visible and properly styled */
    .mobile-sidebar-toggle,
    #sidebar-toggle,
    #right-sidebar-toggle {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 44px !important;
        height: 44px !important;
        background: var(--bg-primary) !important;
        border: 1px solid var(--border-primary) !important;
        border-radius: 8px !important;
        color: var(--text-primary) !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        margin: 0 4px !important;
    }

    .mobile-sidebar-toggle:hover,
    #sidebar-toggle:hover,
    #right-sidebar-toggle:hover {
        background: var(--bg-tertiary) !important;
        border-color: var(--border-secondary) !important;
    }

    /* Custom scrollbar styling for mobile */
    ::-webkit-scrollbar {
        width: 0px !important;
        background: transparent !important;
    }

    /* For Firefox */
    * {
        scrollbar-width: none !important;
    }
}

/* Fix viewport issues on mobile */
@viewport {
    width: device-width;
    zoom: 1.0;
}

/* Additional mobile optimizations */
@media (max-width: 992px) {
    /* Hide the fixed game header on mobile to remove white space */
    .game-header {
        display: none !important;
    }

    /* Hide hover header completely */
    .hover-header {
        display: none !important;
    }

    /* MOBILE SIDEBAR CONTENT STYLING */
    .sidebar .logo h1,
    .right-sidebar .right-sidebar-title {
        font-size: 1.5rem !important;
        margin-bottom: 1.5rem !important;
        text-align: center !important;
        color: white !important;
    }

    .sidebar .character-info,
    .sidebar .game-stats,
    .sidebar .context-info,
    .sidebar .instructions,
    .right-sidebar .role-progression-container {
        margin-bottom: 1.5rem !important;
        padding: 1rem !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border-radius: 8px !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    /* Make buttons more touch-friendly */
    .sidebar button,
    .right-sidebar button {
        min-height: 44px !important;
        padding: 0.75rem 1rem !important;
        font-size: 1rem !important;
        border-radius: 6px !important;
        margin: 0.5rem 0 !important;
    }

    /* Add title to mobile header */
    .header::after {
        content: "Corporate Prompt Master" !important;
        color: var(--text-primary) !important;
        font-weight: bold !important;
        font-size: 1.1rem !important;
        position: absolute !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    /* Ensure hamburger menu is visible */
    #sidebar-toggle {
        display: flex !important;
        order: -1 !important;
        z-index: 1001 !important;
    }

    /* MOBILE RIGHT SIDEBAR TOGGLE - MATCHING LEFT SIDEBAR STYLE */
    .right-sidebar-toggle,
    #right-sidebar-toggle,
    div.right-sidebar-toggle,
    div#right-sidebar-toggle {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        align-items: center !important;
        justify-content: center !important;
        width: 44px !important;
        height: 44px !important;
        background: var(--primary-color) !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        border: none !important;
        padding: 0 !important;
        position: relative !important;
        z-index: 1002 !important;
    }

    .right-sidebar-toggle:hover,
    .right-sidebar-toggle:focus,
    #right-sidebar-toggle:hover,
    #right-sidebar-toggle:focus {
        background: var(--primary-dark) !important;
        transform: scale(1.05) !important;
    }

    /* Right sidebar toggle hamburger lines */
    .right-sidebar-toggle span,
    #right-sidebar-toggle span {
        display: block !important;
        width: 20px !important;
        height: 2px !important;
        background: white !important;
        margin: 3px 0 !important;
        transition: 0.3s !important;
        border-radius: 1px !important;
    }

    /* Right sidebar toggle shortcut hint */
    .right-sidebar-toggle .shortcut-hint,
    #right-sidebar-toggle .shortcut-hint {
        position: absolute !important;
        bottom: -25px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        background: rgba(0, 0, 0, 0.8) !important;
        color: white !important;
        padding: 4px 8px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
        white-space: nowrap !important;
        opacity: 0 !important;
        transition: opacity 0.3s ease !important;
        pointer-events: none !important;
        z-index: 1003 !important;
    }

    .right-sidebar-toggle:hover .shortcut-hint,
    #right-sidebar-toggle:hover .shortcut-hint {
        opacity: 1 !important;
    }

    /* Hide shortcut hints when forcibly hidden */
    .shortcut-hint.forcibly-hidden {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
        position: absolute !important;
        left: -9999px !important;
        width: 0px !important;
        height: 0px !important;
        overflow: hidden !important;
    }

    /* OVERRIDE HIDE-RIGHT-TOGGLE.CSS - FORCE RIGHT TOGGLE VISIBLE ON MOBILE */
    .right-sidebar-toggle,
    #right-sidebar-toggle,
    div.right-sidebar-toggle,
    div#right-sidebar-toggle,
    button.right-sidebar-toggle,
    [class*="right-toggle"],
    [class*="toggle-right"],
    [id*="right-toggle"],
    [id*="toggle-right"] {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        pointer-events: auto !important;
        position: relative !important;
        left: auto !important;
        width: 44px !important;
        height: 44px !important;
        overflow: visible !important;
    }



    /* RIGHT SIDEBAR HEADER STYLING TO MATCH LEFT SIDEBAR */
    .right-sidebar .right-sidebar-header {
        background: rgba(255, 255, 255, 0.1) !important;
        padding: 15px 20px !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
        margin-bottom: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
    }

    .right-sidebar .right-sidebar-title {
        color: #ffffff !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        margin: 0 !important;
    }

    .right-sidebar .right-sidebar-collapse-indicator {
        color: #ffffff !important;
        opacity: 0.7 !important;
        font-size: 12px !important;
    }

    /* RIGHT SIDEBAR CONTENT STYLING */
    .right-sidebar .org-chart-container,
    .right-sidebar .role-progression-container {
        padding: 20px !important;
        color: #ffffff !important;
    }

    .right-sidebar h3 {
        color: #ffffff !important;
        font-size: 14px !important;
        font-weight: 600 !important;
        margin: 0 0 15px 0 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    /* Fallback content styling */
    .right-sidebar .org-chart-fallback,
    .right-sidebar .role-progression-fallback {
        color: #ffffff !important;
        padding: 15px !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border-radius: 8px !important;
        margin: 10px 0 !important;
    }

    .right-sidebar .role-box {
        background: rgba(255, 255, 255, 0.15) !important;
        padding: 12px !important;
        margin: 8px 0 !important;
        border-radius: 6px !important;
        border-left: 3px solid #007bff !important;
    }

    .right-sidebar .role-box.current {
        border-left-color: #28a745 !important;
        background: rgba(40, 167, 69, 0.2) !important;
    }

    .right-sidebar .role-name {
        font-weight: 600 !important;
        font-size: 14px !important;
        margin-bottom: 4px !important;
    }

    .right-sidebar .role-status {
        font-size: 12px !important;
        opacity: 0.8 !important;
    }

    .right-sidebar .current-role,
    .right-sidebar .next-role {
        margin: 10px 0 !important;
        font-size: 14px !important;
    }

    .right-sidebar .career-progress {
        margin: 15px 0 !important;
    }

    .right-sidebar .progress-bar {
        width: 100% !important;
        height: 8px !important;
        background: rgba(255, 255, 255, 0.2) !important;
        border-radius: 4px !important;
        overflow: hidden !important;
        margin: 5px 0 !important;
    }

    .right-sidebar .progress-fill {
        height: 100% !important;
        background: linear-gradient(90deg, #28a745, #20c997) !important;
        transition: width 0.3s ease !important;
    }

    /* RIGHT SIDEBAR BUTTONS AND INTERACTIVE ELEMENTS */
    .right-sidebar .toggle-button {
        background: rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        padding: 8px 12px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
    }

    .right-sidebar .toggle-button:hover {
        background: rgba(255, 255, 255, 0.2) !important;
        border-color: rgba(255, 255, 255, 0.3) !important;
    }

    /* Ensure hamburger menu is properly styled */
    #sidebar-toggle,
    .mobile-sidebar-toggle {
        display: flex !important;
        order: -1 !important;
        z-index: 1001 !important;
        width: 40px !important;
        height: 40px !important;
        cursor: pointer !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border-radius: 6px !important;
        align-items: center !important;
        justify-content: center !important;
        transition: background 0.2s ease !important;
        margin-right: 1rem !important;
    }

    #sidebar-toggle:hover,
    .mobile-sidebar-toggle:hover {
        background: rgba(255, 255, 255, 0.2) !important;
    }

    /* Mobile sidebar toggle */
    .mobile-sidebar-toggle {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 44px !important;
        height: 44px !important;
        background: var(--primary-color) !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        border: none !important;
        padding: 0 !important;
    }

    .mobile-sidebar-toggle:hover,
    .mobile-sidebar-toggle:focus {
        background: var(--primary-dark) !important;
        transform: scale(1.05) !important;
    }

    .mobile-sidebar-toggle span {
        display: block !important;
        width: 20px !important;
        height: 2px !important;
        background: white !important;
        margin: 3px 0 !important;
        transition: 0.3s !important;
        border-radius: 1px !important;
    }

    /* RIGHT SIDEBAR TOGGLE - INHERITS MOBILE-SIDEBAR-TOGGLE STYLES */

    /* Messages container mobile - fill remaining space */
    .messages-container {
        flex: 1 !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
        padding: 1rem !important;
        min-height: 0 !important;
        scrollbar-width: thin !important;
        height: calc(100vh - 56px - 80px) !important; /* viewport - header - input area */
        max-height: calc(100vh - 56px - 80px) !important;
    }

    /* Hide scrollbar for messages container on mobile */
    .messages-container::-webkit-scrollbar {
        width: 3px !important;
        background: transparent !important;
    }

    .messages-container::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3) !important;
        border-radius: 3px !important;
    }

    /* Input area mobile - fixed at bottom */
    .input-area {
        flex-shrink: 0 !important;
        padding: 1rem !important;
        background: var(--bg-secondary) !important;
        border-top: 1px solid var(--border-primary) !important;
        min-height: 80px !important;
        max-height: 80px !important;
    }

    /* Form controls mobile */
    .prompt-input, .response-editor {
        font-size: 16px !important; /* Prevent zoom on iOS */
        min-height: 44px !important;
        padding: 0.75rem !important;
        border-radius: 8px !important;
        border: 2px solid var(--border-primary) !important;
        width: 100% !important;
        resize: vertical !important;
    }

    .prompt-input:focus, .response-editor:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
        outline: none !important;
    }

    /* Buttons mobile */
    .btn, .preview-button, .primary-button, .secondary-button {
        min-height: 44px !important;
        padding: 0.75rem 1.25rem !important;
        font-size: 1rem !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        touch-action: manipulation !important;
    }

    /* Button groups mobile */
    .preview-actions, .edit-actions {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 0.5rem !important;
        margin-top: 1rem !important;
    }

    /* Messages mobile */
    .message {
        max-width: 95% !important;
        margin-bottom: 1rem !important;
        border-radius: 12px !important;
    }

    .message-content {
        padding: 0.75rem 1rem !important;
        font-size: 0.95rem !important;
        line-height: 1.4 !important;
    }

    .message-sender {
        font-size: 0.8rem !important;
        margin-bottom: 0.25rem !important;
    }

    /* Character info mobile */
    .character-info {
        padding: 0.75rem !important;
    }

    .character-name {
        font-size: 1rem !important;
        font-weight: 600 !important;
    }

    .character-title {
        font-size: 0.85rem !important;
        opacity: 0.8 !important;
    }

    /* Hide instructions on mobile to save space */
    .instructions {
        display: none !important;
    }

    /* Modal mobile optimizations */
    .modal-dialog {
        margin: 0.5rem !important;
        max-width: calc(100% - 1rem) !important;
    }

    .modal-content {
        border-radius: 12px !important;
    }

    .modal-body {
        padding: 1.5rem !important;
    }

    .modal-buttons {
        display: flex !important;
        flex-direction: column !important;
        gap: 0.75rem !important;
    }

    .modal-buttons .btn {
        width: 100% !important;
    }
}

/* Small mobile devices (phones) */
@media (max-width: 576px) {
    .sidebar {
        max-height: 200px !important;
        padding: 0.5rem !important;
    }

    .main-content {
        height: calc(100vh - 200px) !important;
    }

    .header {
        padding: 0.5rem !important;
    }

    .messages-container {
        padding: 0.75rem !important;
    }

    .input-area {
        padding: 0.75rem !important;
    }

    .prompt-input, .response-editor {
        height: 70px !important;
        padding: 0.5rem !important;
    }

    .btn, .preview-button, .primary-button, .secondary-button {
        padding: 0.5rem 1rem !important;
        font-size: 0.9rem !important;
    }

    .character-info {
        padding: 0.5rem !important;
    }

    .character-name {
        font-size: 0.9rem !important;
    }

    .character-title {
        font-size: 0.8rem !important;
    }

    .message-content {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.9rem !important;
    }
}

/* Landscape orientation fixes */
@media (max-width: 992px) and (orientation: landscape) {
    .sidebar {
        max-height: 150px !important;
    }

    .main-content {
        height: calc(100vh - 150px) !important;
    }
}

/* Touch device optimizations */
@media (pointer: coarse) {
    /* Larger touch targets */
    .btn, button, .mobile-sidebar-toggle {
        min-height: 48px !important;
        min-width: 48px !important;
    }

    /* Prevent text selection on UI elements */
    .mobile-sidebar-toggle, .btn, button {
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
    }

    /* Improve scrolling on touch devices */
    .messages-container, .sidebar {
        -webkit-overflow-scrolling: touch !important;
        scroll-behavior: smooth !important;
    }
}

/* High DPI display fixes */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .app-container, .main-content, .sidebar {
        height: 100vh !important;
        min-height: 100vh !important;
    }
}

/* ===== DESKTOP RIGHT SIDEBAR BEHAVIOR - COPY LEFT SIDEBAR LOGIC ===== */
@media (min-width: 993px) {
    /* Right sidebar hidden state on desktop - SAME AS LEFT SIDEBAR */
    body.right-sidebar-hidden .right-sidebar {
        transform: translateX(300px) !important;
        width: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
        border-left: none !important;
    }

    /* Adjust main content when right sidebar is hidden - SAME AS LEFT SIDEBAR */
    body.right-sidebar-hidden .main-content {
        margin-right: 0 !important;
    }

    /* Right sidebar toggle button styling on desktop */
    .right-sidebar-toggle {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
}
